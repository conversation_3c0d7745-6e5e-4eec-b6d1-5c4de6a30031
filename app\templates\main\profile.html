{% extends 'base.html' %}

{% block title %}Profile - OTP Authentication{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-4 mb-4">
        <div class="card shadow-sm">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">Profile Information</h5>
            </div>
            <div class="card-body text-center">
                <div class="avatar mb-3">
                    <i class="fas fa-user-circle fa-6x text-primary"></i>
                </div>
                <h4>{{ current_user.get_display_name() }}</h4>
                {% if current_user.username %}
                <p class="text-muted">
                    <i class="fas fa-user me-1"></i>@{{ current_user.username }}
                </p>
                {% endif %}
                <p class="text-muted">
                    <i class="fas fa-phone me-1"></i>{{ current_user.phone_number }}
                </p>
                <p class="text-muted">
                    <i class="fas fa-shield-alt me-1"></i>
                    {% if current_user.is_verified %}
                    <span class="text-success">Verified Account</span>
                    {% else %}
                    <span class="text-warning">Unverified Account</span>
                    {% endif %}
                </p>
                <p class="text-muted">
                    <i class="fas fa-calendar-alt me-1"></i>
                    Joined: {{ current_user.created_at.strftime('%B %d, %Y') }}
                </p>
                <p class="text-muted">
                    <i class="fas fa-sign-in-alt me-1"></i>
                    Total Logins: <span class="fw-bold text-primary">{{ total_activities }}</span>
                </p>
                <div class="mt-3">
                    <button class="btn btn-outline-primary btn-sm" data-bs-toggle="modal"
                        data-bs-target="#editProfileModal">
                        <i class="fas fa-edit me-1"></i>Edit Profile
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-8">
        <div class="card shadow-sm mb-4">
            <div class="card-header bg-light">
                <h5 class="mb-0">Account Security</h5>
            </div>
            <div class="card-body">
                <div class="mb-4">
                    <h6>Two-Factor Authentication</h6>
                    <p class="text-muted">Your account is secured with OTP-based two-factor authentication.</p>
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="twoFactorEnabled" checked disabled>
                        <label class="form-check-label" for="twoFactorEnabled">Enabled (Required)</label>
                    </div>
                </div>

                <hr>

                <div class="mb-4">
                    <h6>Change Phone Number</h6>
                    <p class="text-muted">Update the phone number associated with your account.</p>
                    <button class="btn btn-outline-primary btn-sm" disabled>Coming Soon</button>
                </div>

                <hr>

                <div>
                    <h6>Account Password</h6>
                    <p class="text-muted">Set a password for additional account security.</p>
                    <button class="btn btn-outline-primary btn-sm" disabled>Coming Soon</button>
                </div>
            </div>
        </div>

        <div class="card shadow-sm">
            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Recent Login Activities</h5>
                <small class="text-muted">Last 3 activities</small>
            </div>
            <div class="card-body">
                <p class="text-muted">Track your recent login activities to ensure account security.</p>

                {% if recent_activities %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th><i class="fas fa-calendar me-1"></i>Date & Time</th>
                                <th><i class="fas fa-laptop me-1"></i>Device</th>
                                <th><i class="fas fa-globe me-1"></i>IP Address</th>
                                <th><i class="fas fa-check-circle me-1"></i>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for activity in recent_activities %}
                            <tr>
                                <td>
                                    <div class="fw-bold">
                                        {{ activity.get_ist_login_time().strftime('%b %d, %Y') }}
                                    </div>
                                    <small class="text-muted">
                                        {{ activity.get_ist_login_time().strftime('%I:%M %p IST') }}
                                    </small>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        {% if 'Mobile' in (activity.device_info or '') %}
                                        <i class="fas fa-mobile-alt me-2 text-primary"></i>
                                        {% elif 'Tablet' in (activity.device_info or '') %}
                                        <i class="fas fa-tablet-alt me-2 text-info"></i>
                                        {% else %}
                                        <i class="fas fa-desktop me-2 text-secondary"></i>
                                        {% endif %}
                                        <div>
                                            <div class="fw-bold">{{ activity.get_device_type() }}</div>
                                            <small class="text-muted">{{ activity.get_browser_name() }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <code class="bg-light text-dark p-1 rounded">
                                        {{ activity.ip_address or 'Unknown' }}
                                    </code>
                                </td>
                                <td>
                                    {% if activity.success %}
                                    <span class="badge bg-success">
                                        <i class="fas fa-check me-1"></i>Success
                                    </span>
                                    {% else %}
                                    <span class="badge bg-danger">
                                        <i class="fas fa-times me-1"></i>Failed
                                    </span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                {% if total_activities > 3 %}
                <div class="text-center mt-3">
                    <a href="{{ url_for('main.login_activity') }}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-eye me-1"></i>See More
                        <span class="badge bg-primary ms-1">{{ total_activities - 3 }}+</span>
                    </a>
                </div>
                {% endif %}

                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-history fa-3x text-muted mb-3"></i>
                    <h6 class="text-muted">No login activity found</h6>
                    <p class="text-muted mb-0">Your login history will appear here once you start logging in.</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Edit Profile Modal -->
<div class="modal fade" id="editProfileModal" tabindex="-1" aria-labelledby="editProfileModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editProfileModalLabel">
                    <i class="fas fa-edit me-2"></i>Edit Profile
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST" action="{{ url_for('main.update_profile') }}" id="editProfileForm">
                <div class="modal-body">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">

                    <div class="mb-3">
                        <label for="edit_username" class="form-label">Username</label>
                        <div class="input-group">
                            <span class="input-group-text">@</span>
                            <input type="text" class="form-control" id="edit_username" name="username"
                                value="{{ current_user.username or '' }}" maxlength="30">
                        </div>
                        <div class="form-text">3-30 characters, letters, numbers, underscores, and dots only</div>
                        <div id="edit-username-feedback" class="invalid-feedback"></div>
                    </div>

                    <div class="mb-3">
                        <label for="edit_display_name" class="form-label">Display Name</label>
                        <input type="text" class="form-control" id="edit_display_name" name="display_name"
                            value="{{ current_user.display_name or '' }}" maxlength="50">
                        <div class="form-text">How others will see your name (optional)</div>
                    </div>

                    <div class="mb-3">
                        <label for="phone_display" class="form-label">Phone Number</label>
                        <input type="text" class="form-control" id="phone_display"
                            value="{{ current_user.phone_number }}" disabled>
                        <div class="form-text">Phone number cannot be changed</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary" id="saveProfileBtn">
                        <i class="fas fa-save me-1"></i>Save Changes
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function () {
        const editUsernameInput = document.getElementById('edit_username');
        const editUsernameFeedback = document.getElementById('edit-username-feedback');
        const saveBtn = document.getElementById('saveProfileBtn');

        let usernameTimeout;
        const originalUsername = '{{ current_user.username or "" }}';

        editUsernameInput.addEventListener('input', function () {
            clearTimeout(usernameTimeout);
            const username = this.value.trim().toLowerCase();

            // Clear previous feedback
            this.classList.remove('is-valid', 'is-invalid');
            editUsernameFeedback.textContent = '';

            if (username.length === 0 || username === originalUsername) {
                return;
            }

            // Basic client-side validation
            if (username.length < 3) {
                showEditUsernameError('Username must be at least 3 characters');
                return;
            }

            if (username.length > 30) {
                showEditUsernameError('Username must be less than 30 characters');
                return;
            }

            if (!/^[a-z0-9._]+$/.test(username)) {
                showEditUsernameError('Username can only contain letters, numbers, underscores, and dots');
                return;
            }

            if (username.startsWith('.') || username.endsWith('.')) {
                showEditUsernameError('Username cannot start or end with a dot');
                return;
            }

            // Check availability after a delay
            usernameTimeout = setTimeout(() => {
                checkEditUsernameAvailability(username);
            }, 500);
        });

        function showEditUsernameError(message) {
            editUsernameInput.classList.add('is-invalid');
            editUsernameFeedback.textContent = message;
        }

        function showEditUsernameSuccess(message) {
            editUsernameInput.classList.add('is-valid');
            editUsernameFeedback.textContent = message;
            editUsernameFeedback.className = 'valid-feedback';
        }

        function checkEditUsernameAvailability(username) {
            fetch('/auth/check-username', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('[name=csrf_token]').value
                },
                body: JSON.stringify({ username: username })
            })
                .then(response => response.json())
                .then(data => {
                    if (data.available) {
                        showEditUsernameSuccess('Username is available!');
                    } else {
                        showEditUsernameError('Username is already taken');
                    }
                })
                .catch(error => {
                    console.error('Error checking username:', error);
                });
        }
    });
</script>
{% endblock %}