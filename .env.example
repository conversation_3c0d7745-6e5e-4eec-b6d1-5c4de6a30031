# Flask Configuration
FLASK_APP=run.py
FLASK_ENV=development
SECRET_KEY=your_secret_key_here

# Database Configuration
# For SQLite (development)
DATABASE_URL=sqlite:///app.db
# For PostgreSQL (production)
# DATABASE_URL=postgresql://username:password@localhost:5432/otpauth

# Redis Configuration
REDIS_URL=redis://localhost:6379/0

# Twilio Configuration
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=your_twilio_phone_number

# Logging Configuration
LOG_LEVEL=INFO
