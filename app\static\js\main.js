/**
 * Main JavaScript for OTP Authentication Application
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize Bootstrap tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Initialize Bootstrap popovers
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function(popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });

    // Handle phone number input formatting
    const phoneInput = document.getElementById('phone_number');
    if (phoneInput) {
        phoneInput.addEventListener('input', function(e) {
            // Basic validation for phone number input
            // This is a simple implementation - consider using a library like libphonenumber-js for production
            let input = e.target.value.replace(/\D/g, '');
            if (input.length > 0 && !input.startsWith('+')) {
                input = '+' + input;
            }
            
            // Limit length
            if (input.length > 15) {
                input = input.substring(0, 15);
            }
            
            e.target.value = input;
        });
    }

    // Handle OTP input
    const otpInput = document.getElementById('otp');
    if (otpInput) {
        // Focus on OTP input when page loads
        otpInput.focus();
        
        // Only allow numbers
        otpInput.addEventListener('input', function(e) {
            this.value = this.value.replace(/\D/g, '');
            
            // Auto-submit when all digits are entered
            if (this.value.length === 6) {
                // Optionally auto-submit
                // document.querySelector('form').submit();
                
                // Add visual feedback
                this.classList.add('is-valid');
            } else {
                this.classList.remove('is-valid');
            }
        });
        
        // Start countdown timer for OTP expiration
        let timeLeft = 300; // 5 minutes in seconds
        const timerElement = document.getElementById('otp-timer');
        
        if (timerElement) {
            const countdownTimer = setInterval(function() {
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                
                timerElement.textContent = `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;
                
                if (timeLeft <= 0) {
                    clearInterval(countdownTimer);
                    timerElement.textContent = 'Expired';
                    timerElement.classList.add('text-danger');
                    
                    // Optionally disable the form
                    const submitButton = document.querySelector('button[type="submit"]');
                    if (submitButton) {
                        submitButton.disabled = true;
                        submitButton.textContent = 'OTP Expired';
                    }
                }
                
                timeLeft--;
            }, 1000);
        }
    }

    // Handle flash messages auto-dismiss
    const flashMessages = document.querySelectorAll('.alert');
    flashMessages.forEach(function(flash) {
        setTimeout(function() {
            const bsAlert = new bootstrap.Alert(flash);
            bsAlert.close();
        }, 5000); // Auto-dismiss after 5 seconds
    });

    // Add animation to dashboard cards
    const dashboardCards = document.querySelectorAll('.dashboard-card');
    dashboardCards.forEach(function(card, index) {
        setTimeout(function() {
            card.classList.add('show');
        }, index * 100); // Stagger animation
    });
});

// Function to toggle password visibility
function togglePasswordVisibility(inputId, iconId) {
    const passwordInput = document.getElementById(inputId);
    const icon = document.getElementById(iconId);
    
    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        passwordInput.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}
