{% extends 'base.html' %}

{% block title %}Home - OTP Authentication{% endblock %}

{% block content %}
<div class="row align-items-center py-5">
    <div class="col-lg-6 order-lg-2">
        <img src="https://cdn.pixabay.com/photo/2017/01/31/16/42/security-2025593_1280.png" alt="Secure Authentication" class="img-fluid rounded shadow">
    </div>
    <div class="col-lg-6 order-lg-1 py-4">
        <h1 class="display-4 fw-bold text-primary">Secure OTP Authentication</h1>
        <p class="lead">Enhance your application security with our One-Time Password verification system.</p>
        <p>Our OTP-based authentication provides an additional layer of security to protect your account from unauthorized access.</p>
        
        <div class="features mt-4">
            <div class="d-flex align-items-center mb-3">
                <div class="feature-icon bg-primary text-white rounded-circle p-3 me-3">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <div>
                    <h5 class="mb-0">Enhanced Security</h5>
                    <p class="text-muted mb-0">Two-factor authentication with OTP verification</p>
                </div>
            </div>
            
            <div class="d-flex align-items-center mb-3">
                <div class="feature-icon bg-primary text-white rounded-circle p-3 me-3">
                    <i class="fas fa-mobile-alt"></i>
                </div>
                <div>
                    <h5 class="mb-0">SMS Delivery</h5>
                    <p class="text-muted mb-0">Receive secure codes directly to your phone</p>
                </div>
            </div>
            
            <div class="d-flex align-items-center">
                <div class="feature-icon bg-primary text-white rounded-circle p-3 me-3">
                    <i class="fas fa-user-lock"></i>
                </div>
                <div>
                    <h5 class="mb-0">User-Friendly</h5>
                    <p class="text-muted mb-0">Simple and intuitive authentication process</p>
                </div>
            </div>
        </div>
        
        <div class="mt-4">
            {% if current_user.is_authenticated %}
                <a href="{{ url_for('main.dashboard') }}" class="btn btn-primary btn-lg">Go to Dashboard</a>
            {% else %}
                <a href="{{ url_for('auth.register') }}" class="btn btn-primary btn-lg me-2">Register</a>
                <a href="{{ url_for('auth.login') }}" class="btn btn-outline-primary btn-lg">Login</a>
            {% endif %}
        </div>
    </div>
</div>

<hr class="my-5">

<div class="row text-center py-4">
    <div class="col-12">
        <h2 class="mb-4">How It Works</h2>
    </div>
    <div class="col-md-4 mb-4">
        <div class="card h-100 shadow-sm">
            <div class="card-body">
                <div class="icon-wrapper mb-3">
                    <i class="fas fa-user-plus fa-3x text-primary"></i>
                </div>
                <h4>1. Register</h4>
                <p>Create an account with your phone number</p>
            </div>
        </div>
    </div>
    <div class="col-md-4 mb-4">
        <div class="card h-100 shadow-sm">
            <div class="card-body">
                <div class="icon-wrapper mb-3">
                    <i class="fas fa-sms fa-3x text-primary"></i>
                </div>
                <h4>2. Receive OTP</h4>
                <p>Get a 6-digit verification code via SMS</p>
            </div>
        </div>
    </div>
    <div class="col-md-4 mb-4">
        <div class="card h-100 shadow-sm">
            <div class="card-body">
                <div class="icon-wrapper mb-3">
                    <i class="fas fa-check-circle fa-3x text-primary"></i>
                </div>
                <h4>3. Verify & Access</h4>
                <p>Enter the code to securely access your account</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .feature-icon {
        width: 50px;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .icon-wrapper {
        height: 80px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
</style>
{% endblock %}
