{% extends 'base.html' %}

{% block title %}Verify OTP - OTP Authentication{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6 col-lg-5">
        <div class="card shadow">
            <div class="card-header bg-primary text-white text-center py-3">
                <h4 class="mb-0"><i class="fas fa-key me-2"></i>Verify OTP</h4>
            </div>
            <div class="card-body p-4">
                <div class="text-center mb-4">
                    <div class="alert alert-success">
                        <i class="fas fa-shield-alt me-2"></i>
                        <strong>OTP Verification</strong><br>
                        Enter the 6-digit verification code for:<br>
                        <strong class="text-primary">{{ phone_number }}</strong>
                    </div>

                    <p class="text-muted small">
                        <i class="fas fa-clock me-1"></i>
                        Code expires in 5 minutes
                    </p>
                </div>

                <form method="POST" action="{{ url_for('auth.verify_otp') }}">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">

                    <div class="mb-4">
                        <label for="otp" class="form-label">Enter Verification Code</label>
                        <div class="otp-input-container d-flex justify-content-center">
                            <input type="text" class="form-control text-center fs-4" id="otp" name="otp" maxlength="6"
                                pattern="[0-9]{6}" inputmode="numeric" autocomplete="one-time-code" placeholder="000000"
                                style="letter-spacing: 0.5em; width: 200px;" required>
                        </div>
                        <div class="form-text text-center">Enter the 6-digit code from your phone</div>
                    </div>

                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">Verify</button>
                    </div>
                </form>

                <div class="text-center mt-4">
                    <div class="border-top pt-3">
                        <p class="mb-2 text-muted">Didn't receive the code?</p>
                        <form method="POST" action="{{ url_for('auth.resend_otp') }}" class="d-inline">
                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                            <button type="submit" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-redo me-1"></i>Resend OTP
                            </button>
                        </form>
                        <div class="mt-2">
                            <a href="{{ url_for('auth.login') }}" class="btn btn-link btn-sm text-muted">
                                <i class="fas fa-arrow-left me-1"></i>Back to Login
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function () {
        const otpInput = document.getElementById('otp');
        const form = document.querySelector('form');

        // Auto focus on OTP input
        otpInput.focus();

        // Only allow numbers
        otpInput.addEventListener('input', function (e) {
            // Remove any non-digit characters
            this.value = this.value.replace(/\D/g, '');

            // Auto-submit when all 6 digits are entered
            if (this.value.length === 6) {
                // Add a small delay for better UX
                setTimeout(() => {
                    form.submit();
                }, 300);
            }
        });

        // Prevent non-numeric input
        otpInput.addEventListener('keypress', function (e) {
            if (!/[0-9]/.test(e.key) && !['Backspace', 'Delete', 'Tab', 'Enter'].includes(e.key)) {
                e.preventDefault();
            }
        });

        // Handle paste events
        otpInput.addEventListener('paste', function (e) {
            e.preventDefault();
            const paste = (e.clipboardData || window.clipboardData).getData('text');
            const numbers = paste.replace(/\D/g, '').substring(0, 6);
            this.value = numbers;

            if (numbers.length === 6) {
                setTimeout(() => {
                    form.submit();
                }, 300);
            }
        });
    });
</script>
{% endblock %}