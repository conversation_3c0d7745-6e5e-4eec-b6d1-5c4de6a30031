{% extends 'base.html' %}

{% block title %}Login - OTP Authentication{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6 col-lg-5">
        <div class="card shadow">
            <div class="card-header bg-primary text-white text-center py-3">
                <h4 class="mb-0"><i class="fas fa-sign-in-alt me-2"></i>Login</h4>
            </div>
            <div class="card-body p-4">
                <form method="POST" action="{{ url_for('auth.login') }}">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <div class="mb-3">
                        <label for="identifier" class="form-label">Username or Phone Number</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-user"></i></span>
                            <input type="text" class="form-control" id="identifier" name="identifier"
                                placeholder="Enter username or phone number" required>
                        </div>
                        <div class="form-text">You can login with either your username or phone number</div>
                    </div>
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">Send OTP</button>
                    </div>
                </form>
            </div>
            <div class="card-footer bg-light text-center py-3">
                <p class="mb-0">Don't have an account? <a href="{{ url_for('auth.register') }}">Register</a></p>
                {% if config.DEBUG %}
                <p class="mb-0 mt-2">
                    <small class="text-muted">Development: </small>
                    <a href="{{ url_for('auth.unlock_account') }}" class="text-warning">
                        <i class="fas fa-unlock"></i> Unlock Account
                    </a>
                </p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Add international phone number formatting if needed
</script>
{% endblock %}