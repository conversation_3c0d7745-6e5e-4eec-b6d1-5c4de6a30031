/* Main Styles for OTP Authentication App */

/* Custom Variables */
:root {
    --primary-color: #4e73df;
    --secondary-color: #858796;
    --success-color: #1cc88a;
    --info-color: #36b9cc;
    --warning-color: #f6c23e;
    --danger-color: #e74a3b;
    --light-color: #f8f9fc;
    --dark-color: #5a5c69;
}

/* General Styles */
body {
    font-family: 'Nunito', 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background-color: #f8f9fc;
    color: #333;
}

.navbar-brand i {
    margin-right: 0.5rem;
}

/* Card Styles */
.card {
    border: none;
    border-radius: 0.5rem;
    transition: all 0.2s;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

.card-header {
    border-top-left-radius: 0.5rem !important;
    border-top-right-radius: 0.5rem !important;
}

/* Form Styles */
.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(78, 115, 223, 0.25);
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: #2e59d9;
    border-color: #2653d4;
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* OTP Input Styling */
.otp-input-container {
    margin: 1rem 0;
}

#otp {
    letter-spacing: 0.5rem;
    font-size: 1.5rem;
    font-weight: bold;
    max-width: 250px;
}

/* Feature Icons */
.feature-icon {
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .order-lg-1 {
        order: 2;
    }
    
    .order-lg-2 {
        order: 1;
    }
}

/* Animation for OTP Verification */
@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.pulse {
    animation: pulse 1.5s infinite;
}

/* Profile Page Styles */
.avatar {
    margin: 1rem 0;
}

/* Dashboard Cards */
.dashboard-card {
    transition: all 0.3s;
}

.dashboard-card:hover {
    transform: translateY(-5px);
}

/* Footer Styles */
footer {
    margin-top: auto;
}
